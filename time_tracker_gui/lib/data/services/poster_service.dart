import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import '../models/poster_model.dart';
import '../../core/constants/api_constants.dart';

class PosterService {
  static final Random _random = Random();

  // Different ordering options for randomization
  static const List<String> _orderingOptions = [
    '-rating',
    '-released',
    '-added',
    '-created',
    '-updated',
    'name',
    '-metacritic',
  ];

  // Fetch popular/trending games with randomization
  Future<List<Poster>> fetchPosters({int page = 1, int pageSize = 20}) async {
    try {
      // Randomize the ordering to get different results each time
      final randomOrdering = _orderingOptions[_random.nextInt(_orderingOptions.length)];

      // Also randomize the page number within a reasonable range (1-10)
      // to get different sets of games
      final randomPage = page == 1 ? _random.nextInt(10) + 1 : page;

      final uri = Uri.parse('${ApiConstants.rawgBaseUrl}${ApiConstants.rawgGamesEndpoint}')
          .replace(queryParameters: {
        'key': ApiConstants.rawgApiKey,
        'page': randomPage.toString(),
        'page_size': pageSize.toString(),
        'ordering': randomOrdering,
      });

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        final List<dynamic> results = jsonResponse['results'] ?? [];
        final posters = results.map((poster) => Poster.fromJson(poster)).toList();

        // Shuffle the results for additional randomization
        posters.shuffle(_random);

        return posters;
      } else {
        throw Exception('Failed to load games: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch games: $e');
    }
  }

  // Search games by name
  Future<List<Poster>> searchGames(String query, {int page = 1, int pageSize = 20}) async {
    if (query.trim().isEmpty) {
      return fetchPosters(page: page, pageSize: pageSize);
    }

    try {
      final uri = Uri.parse('${ApiConstants.rawgBaseUrl}${ApiConstants.rawgSearchEndpoint}')
          .replace(queryParameters: {
        'key': ApiConstants.rawgApiKey,
        'search': query.trim(),
        'page': page.toString(),
        'page_size': pageSize.toString(),
      });

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        final List<dynamic> results = jsonResponse['results'] ?? [];
        return results.map((poster) => Poster.fromJson(poster)).toList();
      } else {
        throw Exception('Failed to search games: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to search games: $e');
    }
  }

  // Get games by specific genre
  Future<List<Poster>> getGamesByGenre(String genreId, {int page = 1, int pageSize = 20}) async {
    try {
      final uri = Uri.parse('${ApiConstants.rawgBaseUrl}${ApiConstants.rawgGamesEndpoint}')
          .replace(queryParameters: {
        'key': ApiConstants.rawgApiKey,
        'genres': genreId,
        'page': page.toString(),
        'page_size': pageSize.toString(),
        'ordering': '-rating',
      });

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        final List<dynamic> results = jsonResponse['results'] ?? [];
        return results.map((poster) => Poster.fromJson(poster)).toList();
      } else {
        throw Exception('Failed to load games by genre: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch games by genre: $e');
    }
  }

  // Fetch random games with extra randomization
  Future<List<Poster>> fetchRandomPosters({int pageSize = 20}) async {
    try {
      // Use a completely random page number from 1-50 for maximum variety
      final randomPage = _random.nextInt(50) + 1;
      final randomOrdering = _orderingOptions[_random.nextInt(_orderingOptions.length)];

      final uri = Uri.parse('${ApiConstants.rawgBaseUrl}${ApiConstants.rawgGamesEndpoint}')
          .replace(queryParameters: {
        'key': ApiConstants.rawgApiKey,
        'page': randomPage.toString(),
        'page_size': pageSize.toString(),
        'ordering': randomOrdering,
      });

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        final List<dynamic> results = jsonResponse['results'] ?? [];
        final posters = results.map((poster) => Poster.fromJson(poster)).toList();

        // Double shuffle for maximum randomization
        posters.shuffle(_random);
        posters.shuffle(_random);

        return posters;
      } else {
        throw Exception('Failed to load random games: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch random games: $e');
    }
  }

  // Get game details by ID
  Future<Poster?> getGameById(int gameId) async {
    try {
      final uri = Uri.parse('${ApiConstants.rawgBaseUrl}${ApiConstants.rawgGamesEndpoint}/$gameId')
          .replace(queryParameters: {
        'key': ApiConstants.rawgApiKey,
      });

      final response = await http.get(uri);

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = json.decode(response.body);
        return Poster.fromJson(jsonResponse);
      } else if (response.statusCode == 404) {
        return null; // Game not found
      } else {
        throw Exception('Failed to load game details: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to fetch game details: $e');
    }
  }
} 