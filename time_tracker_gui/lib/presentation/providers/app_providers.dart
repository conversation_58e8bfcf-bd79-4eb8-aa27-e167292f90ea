import 'dart:async';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../data/models/app_model.dart';
import '../../data/repositories/time_tracker_repository.dart';
import '../../data/services/websocket_service.dart';
import 'settings_provider.dart';

// Debug flag to gate logs from this file
const bool _kProvidersDebug = true;
void _d(String msg) {
  if (_kProvidersDebug) {
    // ignore: avoid_print
    print('[providers] $msg');
  }
}

// ---------- Small shared helpers (reduce duplication) ----------

bool _isWsRepo(TimeTrackerRepository r) => r is WebSocketTimeTrackerRepository;
WebSocketTimeTrackerRepository? _asWs(TimeTrackerRepository r) =>
    r is WebSocketTimeTrackerRepository ? r : null;

int _compareDateDesc(DateTime? a, DateTime? b) {
  if (a == null && b == null) return 0;
  if (a == null) return 1;
  if (b == null) return -1;
  return b.compareTo(a);
}


void _setLoadingIfNoValue<T>(AsyncValue<T> state, void Function(AsyncValue<T>) setState) {
  if (!state.hasValue) setState(const AsyncValue.loading());
}

// ---------- Service Providers ----------

final webSocketServiceProvider = Provider<WebSocketService>((ref) {
  final settings = ref.watch(settingsProvider);
  final wsUrl = settings.backendUrl.replaceFirst('http', 'ws') + '/ws';
  _d('Creating WebSocketService with wsUrl: $wsUrl');

  final webSocketService = WebSocketService(wsUrl: wsUrl);

  ref.onDispose(() {
    _d('Disposing WebSocketService');
    webSocketService.dispose();
  });

  return webSocketService;
});

// Repository provider - WebSocket implementation
final timeTrackerRepositoryProvider = Provider<TimeTrackerRepository>((ref) {
  final webSocketService = ref.watch(webSocketServiceProvider);
  _d('Creating WebSocketTimeTrackerRepository');
  final repository = WebSocketTimeTrackerRepository(webSocketService: webSocketService);

  ref.onDispose(() {
    _d('Disposing WebSocketTimeTrackerRepository');
    repository.dispose();
  });

  return repository;
});

// Settings change listener that invalidates providers
final settingsChangeProvider = Provider<void>((ref) {
  ref.listen(settingsProvider, (previous, next) {
    if (previous != null && previous.backendUrl != next.backendUrl) {
      _d('Settings changed: ${previous.backendUrl} -> ${next.backendUrl}');
      ref.invalidate(webSocketServiceProvider);
      ref.invalidate(timeTrackerRepositoryProvider);
    }
  });
});

// ---------- State Providers ----------

final appsProvider =
    StateNotifierProvider<AppsNotifier, AsyncValue<List<AppModel>>>((ref) {
  final repository = ref.watch(timeTrackerRepositoryProvider);
  return AppsNotifier(repository);
});

final trackingStatusProvider = StateNotifierProvider<TrackingStatusNotifier,
    AsyncValue<TrackingStatus>>((ref) {
  return TrackingStatusNotifier(ref.watch(timeTrackerRepositoryProvider));
});

final statisticsProvider = StateNotifierProvider<StatisticsNotifier,
    AsyncValue<List<AppStatistics>>>((ref) {
  return StatisticsNotifier(ref.read(timeTrackerRepositoryProvider));
});

final timelineProvider = StateNotifierProvider<TimelineNotifier,
    AsyncValue<List<TimelineModel>>>((ref) {
  return TimelineNotifier(ref.read(timeTrackerRepositoryProvider));
});

// Cached provider for all-time timeline data with improved timeout handling
final allTimeTimelineProvider = StateNotifierProvider<AllTimeTimelineNotifier,
    AsyncValue<List<TimelineModel>>>((ref) {
  final repo = ref.watch(timeTrackerRepositoryProvider);

  final notifier = AllTimeTimelineNotifier(repo);

  // If repository instance changes later, update the notifier safely
  ref.listen<TimeTrackerRepository>(timeTrackerRepositoryProvider, (prev, next) {
    if (prev != next) {
      notifier.updateRepository(next);
    }
  });

  return notifier;
});

// More efficient provider for session counts per app
final sessionCountsProvider =
    StateNotifierProvider<SessionCountsNotifier, AsyncValue<Map<int, int>>>(
        (ref) {
  return SessionCountsNotifier(ref.read(timeTrackerRepositoryProvider));
});

// Removed unused stream providers - using optimized StateNotifier approach instead

// Derived provider for apps with last used dates - with debug logs and strict null-safe checks
final appsWithLastUsedCacheProvider =
    Provider<AsyncValue<List<AppWithLastUsed>>>((ref) {
  final appsAsync = ref.watch(appsProvider);
  final timelineAsync = ref.watch(allTimeTimelineProvider);

  if (_kProvidersDebug) {
    final String appsState = appsAsync.when(
      data: (_) => 'data',
      loading: () => 'loading',
      error: (e, _) => 'error:$e',
    );
    final String timelineState = timelineAsync.when(
      data: (_) => 'data',
      loading: () => 'loading',
      error: (e, _) => 'error:$e',
    );
    _d('appsWithLastUsedCacheProvider: apps=$appsState timeline=$timelineState');
  }

  // If apps are not ready, we cannot proceed
  if (appsAsync.isLoading) {
    return const AsyncValue.loading();
  }
  if (appsAsync.hasError) {
    final Object error = appsAsync.error ?? Exception('Unknown apps error');
    final StackTrace st = appsAsync.stackTrace ?? StackTrace.empty;
    return AsyncValue.error(error, st);
  }

  // Apps are ready here
  final List<AppModel> apps = appsAsync.requireValue;

  // If timeline has data, use it; otherwise WAIT to ensure correctness
  if (timelineAsync.isLoading) {
    if (_kProvidersDebug) {
      _d('appsWithLastUsedCacheProvider: timeline loading, waiting for full data');
    }
    return const AsyncValue.loading();
  }
  if (timelineAsync.hasError) {
    final Object err = timelineAsync.error ?? Exception('Unknown timeline error');
    final StackTrace st = timelineAsync.stackTrace ?? StackTrace.empty;
    if (_kProvidersDebug) {
      _d('appsWithLastUsedCacheProvider: timeline error ($err), surfacing error');
    }
    return AsyncValue.error(err, st);
  }

  // timeline has value here
  final List<TimelineModel> timeline = timelineAsync.requireValue;

  // Build latest date per appId in a single pass O(n)
  final Map<int, DateTime?> latestByAppId = <int, DateTime?>{};
  for (final TimelineModel t in timeline) {
    final int? id = t.appId;
    if (id == null) {
      continue;
    }
    final DateTime? d = t.date;
    final DateTime? prev = latestByAppId[id];
    if (prev == null) {
      latestByAppId[id] = d;
    } else if (d != null && d.isAfter(prev)) {
      latestByAppId[id] = d;
    }
  }

  final List<AppWithLastUsed> result = <AppWithLastUsed>[
    for (final AppModel a in apps)
      AppWithLastUsed(
        app: a,
        lastUsedDate: a.id != null ? latestByAppId[a.id!] : null,
      ),
  ];

  if (_kProvidersDebug) {
    _d('appsWithLastUsedCacheProvider: produced ${result.length} items with timeline data (waited until ready)');
  }

  return AsyncValue.data(result);
});

// Provider for getting the 5 most recent apps according to the timeline
final recentAppsProvider = Provider<AsyncValue<List<AppModel>>>((ref) {
  final appsWithLastUsed = ref.watch(appsWithLastUsedCacheProvider);

  return appsWithLastUsed.when(
    data: (items) {
      final sorted = List<AppWithLastUsed>.from(items)
        ..sort((a, b) => _compareDateDesc(a.lastUsedDate, b.lastUsedDate));
      final recentApps = sorted.take(5).map((e) => e.app).toList();
      return AsyncValue.data(recentApps);
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) {
      _d('recentAppsProvider error: $error');
      return AsyncValue.error(error, stackTrace);
    },
  );
});

// Connection Status Provider
final connectionStatusProvider =
    StateNotifierProvider<ConnectionStatusNotifier, ConnectionStatus>((ref) {
  final repository = ref.watch(timeTrackerRepositoryProvider);
  final notifier = ConnectionStatusNotifier(repository);

  // Listen for repository changes and update the notifier
  ref.listen(timeTrackerRepositoryProvider, (previous, next) {
    if (previous != next) {
      notifier.updateRepository(next);
    }
  });

  return notifier;
});

// Search and Filter Providers
final appSearchProvider =
    StateNotifierProvider<AppSearchNotifier, AppSearchState>((ref) {
  return AppSearchNotifier();
});

// ---------- Enhanced types ----------

class AppWithLastUsed {
  final AppModel app;
  final DateTime? lastUsedDate;

  const AppWithLastUsed({
    required this.app,
    this.lastUsedDate,
  });
}

// Removed deprecated appsWithLastUsedProvider - replaced by stream-based implementation

final filteredAppsProvider = Provider<AsyncValue<List<AppModel>>>((ref) {
  final appsWithLastUsed = ref.watch(appsWithLastUsedCacheProvider);
  final searchState = ref.watch(appSearchProvider);

  return appsWithLastUsed.when(
    data: (items) {
      Iterable<AppWithLastUsed> filtered = items;

      // Apply search filter
      if (searchState.searchQuery.isNotEmpty) {
        final q = searchState.searchQuery.toLowerCase();
        filtered = filtered.where((e) {
          final app = e.app;
          return (app.name?.toLowerCase().contains(q) ?? false) ||
              (app.productName?.toLowerCase().contains(q) ?? false);
        });
      }

      // Apply sorting
      final list = filtered.toList();
      switch (searchState.sortBy) {
        case AppSortBy.name:
          list.sort((a, b) => (a.app.name ?? '').compareTo(b.app.name ?? ''));
          break;
        case AppSortBy.duration:
          list.sort((a, b) =>
              (b.app.duration ?? 0).compareTo(a.app.duration ?? 0));
          break;
        case AppSortBy.launches:
          list.sort((a, b) =>
              (b.app.launches ?? 0).compareTo(a.app.launches ?? 0));
          break;
        case AppSortBy.lastUsed:
          list.sort(
              (a, b) => _compareDateDesc(a.lastUsedDate, b.lastUsedDate));
          break;
      }

      return AsyncValue.data(list.map((e) => e.app).toList());
    },
    loading: () => const AsyncValue.loading(),
    error: (error, stackTrace) => AsyncValue.error(error, stackTrace),
  );
});

// ---------- State Notifiers ----------

class AppsNotifier extends StateNotifier<AsyncValue<List<AppModel>>> {
  final TimeTrackerRepository _repository;

  AppsNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadApps();
    _listenToAppUpdates();
    _listenToAppsListUpdates();
  }

  Future<void> loadApps() async {
    try {
      if (!mounted) return;
      _setLoadingIfNoValue(state, (v) => state = v);

      if (_isWsRepo(_repository)) {
        final ws = _asWs(_repository)!;
        await ws.connect();
        await ws.getAllApps(); // stream will deliver data
      } else {
        final apps = await _repository.getAllApps();
        if (mounted) state = AsyncValue.data(apps);
      }
    } catch (error) {
      if (!mounted) return;
      if (state.hasValue) {
        _d('AppsNotifier.loadApps failed, keeping cached data: $error');
      } else {
        state = const AsyncValue.data(<AppModel>[]);
      }
    }
  }

  Future<void> addApp(String name) async {
    try {
      await _repository.insertApp(name);
      if (!_isWsRepo(_repository)) {
        await loadApps();
      }
    } catch (error, stackTrace) {
      if (mounted) state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> deleteApp(int appId) async {
    try {
      await _repository.deleteApp(appId);
      if (!_isWsRepo(_repository)) {
        await loadApps();
      }
    } catch (error, stackTrace) {
      if (mounted) state = AsyncValue.error(error, stackTrace);
    }
  }

  void _listenToAppUpdates() {
    _repository.appUpdateStream.listen((updatedApp) {
      if (!mounted) return;
      state.whenData((apps) {
        final updated = apps
            .map((a) => a.id == updatedApp.id ? updatedApp : a)
            .toList(growable: false);
        if (mounted) state = AsyncValue.data(updated);
      });
    });
  }

  void _listenToAppsListUpdates() {
    final ws = _asWs(_repository);
    if (ws == null) return;

    ws.appsListStream.listen(
      (apps) {
        if (mounted) state = AsyncValue.data(apps);
      },
      onError: (error) {
        _d('AppsNotifier: Error in apps list stream: $error (keeping cached state)');
      },
      onDone: () => _d('AppsNotifier: Apps list stream closed'),
    );
  }
}

class TrackingStatusNotifier
    extends StateNotifier<AsyncValue<TrackingStatus>> {
  final TimeTrackerRepository _repository;

  TrackingStatusNotifier(this._repository) : super(const AsyncValue.loading()) {
    _initializeRepository();
    loadTrackingStatus();
    _listenToStatusUpdates();
  }

  Future<void> _initializeRepository() async {
    try {
      await _repository.connect();
    } catch (e) {
      _d('Failed to connect repository: $e');
    }
  }

  Future<void> loadTrackingStatus() async {
    try {
      if (!mounted) return;
      _setLoadingIfNoValue(state, (v) => state = v);

      final status = await _repository.getTrackingStatus();
      if (mounted) state = AsyncValue.data(status);
    } catch (error) {
      if (!mounted) return;
      if (state.hasValue) {
        _d('TrackingStatusNotifier.loadTrackingStatus failed, keeping cached status: $error');
      } else {
        state = const AsyncValue.data(
          TrackingStatus(isTracking: false, isPaused: false),
        );
      }
    }
  }

  Future<void> startTracking() async {
    try {
      await _repository.startTracking();
    } catch (error) {
      _d('TrackingStatusNotifier.startTracking failed: $error');
    }
  }

  Future<void> stopTracking() async {
    try {
      await _repository.stopTracking();
    } catch (error) {
      _d('TrackingStatusNotifier.stopTracking failed: $error');
    }
  }

  Future<void> pauseTracking() async {
    try {
      await _repository.pauseTracking();
    } catch (error) {
      _d('TrackingStatusNotifier.pauseTracking failed: $error');
    }
  }

  Future<void> resumeTracking() async {
    try {
      await _repository.resumeTracking();
    } catch (error) {
      _d('TrackingStatusNotifier.resumeTracking failed: $error');
    }
  }

  void _listenToStatusUpdates() {
    _repository.trackingStatusStream.listen((status) {
      if (mounted) state = AsyncValue.data(status);
    });
  }
}

class StatisticsNotifier
    extends StateNotifier<AsyncValue<List<AppStatistics>>> {
  final TimeTrackerRepository _repository;

  StatisticsNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadStatistics();
    _listenToStatisticsUpdates();
  }

  Future<void> loadStatistics({DateTime? startDate, DateTime? endDate}) async {
    try {
      if (!mounted) return;
      _setLoadingIfNoValue(state, (v) => state = v);

      if (_isWsRepo(_repository)) {
        await _repository.getStatistics(
            startDate: startDate, endDate: endDate);
      } else {
        final statistics = await _repository.getStatistics(
            startDate: startDate, endDate: endDate);
        if (mounted) state = AsyncValue.data(statistics);
      }
    } catch (error) {
      if (!mounted) return;
      if (state.hasValue) {
        _d('StatisticsNotifier.loadStatistics failed, keeping cached data: $error');
      } else {
        state = const AsyncValue.data(<AppStatistics>[]);
      }
    }
  }

  void _listenToStatisticsUpdates() {
    final ws = _asWs(_repository);
    if (ws == null) return;
    ws.statisticsStream.listen((statistics) {
      if (mounted) state = AsyncValue.data(statistics);
    });
  }
}

class TimelineNotifier
    extends StateNotifier<AsyncValue<List<TimelineModel>>> {
  final TimeTrackerRepository _repository;

  TimelineNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadTimeline();
    _listenToTimelineUpdates();
  }

  Future<void> loadTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) async {
    try {
      if (!mounted) return;
      _setLoadingIfNoValue(state, (v) => state = v);

      if (_isWsRepo(_repository)) {
        await _repository.getTimeline(
            startDate: startDate, endDate: endDate, appId: appId);
      } else {
        final timeline = await _repository.getTimeline(
            startDate: startDate, endDate: endDate, appId: appId);
        if (mounted) state = AsyncValue.data(timeline);
      }
    } catch (error) {
      if (!mounted) return;
      if (state.hasValue) {
        _d('TimelineNotifier.loadTimeline failed, keeping cached data: $error');
      } else {
        state = const AsyncValue.data(<TimelineModel>[]);
      }
    }
  }

  void _listenToTimelineUpdates() {
    final ws = _asWs(_repository);
    if (ws == null) return;
    ws.timelineStream.listen((timeline) {
      if (mounted) state = AsyncValue.data(timeline);
    });
  }
}

class AllTimeTimelineNotifier
    extends StateNotifier<AsyncValue<List<TimelineModel>>> {
  WebSocketTimeTrackerRepository _repository;
  bool _requestInFlight = false;
  StreamSubscription<List<TimelineModel>>? _timelineSub;
  StreamSubscription<bool>? _connSub;

  AllTimeTimelineNotifier(TimeTrackerRepository repository)
      : _repository = _asWs(repository)!,
        super(const AsyncValue.loading()) {
    _setup();
  }

  // Allow swapping repository safely (e.g., when settings / WS URL change)
  void updateRepository(TimeTrackerRepository newRepository) {
    final ws = _asWs(newRepository);
    if (ws == null) return;
    if (identical(ws, _repository)) return;

    // Cancel old subscriptions before switching
    try {
      _timelineSub?.cancel();
    } catch (_) {}
    _timelineSub = null;
    try {
      _connSub?.cancel();
    } catch (_) {}
    _connSub = null;

    _repository = ws;
    // Reset request gate so a new fetch can happen on the new repository
    _requestInFlight = false;

    if (!mounted) return;
    // Re-initialize listeners and fetch flow on the new repository
    _setup();
  }

  Future<void> _setup() async {
    // Subscribe to timeline stream first to avoid missing first emission
    _timelineSub = _repository.timelineStream.listen(
      (timeline) {
        if (_kProvidersDebug) {
          _d('AllTimeTimelineNotifier: received timeline items=${timeline.length}');
        }
        if (!mounted) return;
        // Guard against use-after-dispose by re-checking before write
        state = AsyncValue.data(timeline);
      },
      onError: (e, st) {
        if (!mounted) return;
        // Avoid writing if disposed between checks
        if (!state.hasValue) {
          state = const AsyncValue.loading();
        }
        _d('AllTimeTimelineNotifier.timelineStream error: $e');
      },
      onDone: () {
        if (_kProvidersDebug) {
          _d('AllTimeTimelineNotifier.timelineStream done');
        }
      },
      cancelOnError: false,
    );

    // React to connection changes to trigger fetch
    _connSub = _repository.connectionStateStream.listen(
      (isConnected) {
        if (!mounted) return;
        if (_kProvidersDebug) {
          _d('AllTimeTimelineNotifier: connectionState=$isConnected');
        }
        if (isConnected) {
          _requestTimelineIfIdle();
        }
      },
      onError: (err) {
        if (_kProvidersDebug) {
          _d('AllTimeTimelineNotifier: connectionStateStream error: $err');
        }
      },
      onDone: () {
        if (_kProvidersDebug) {
          _d('AllTimeTimelineNotifier.connectionStateStream done');
        }
      },
      cancelOnError: false,
    );

    // Ensure connection
    try {
      await _repository.connect();
    } catch (e) {
      if (_kProvidersDebug) {
        _d('AllTimeTimelineNotifier: connect() failed: $e');
      }
    }

    // If disposed while awaiting connect, bail out
    if (!mounted) return;

    // If connected now, request timeline; otherwise stay loading until connection established
    if (_repository.isConnected) {
      _requestTimelineIfIdle();
    } else {
      if (!state.hasValue) {
        state = const AsyncValue.loading();
      }
    }
  }

  void _requestTimelineIfIdle() {
    if (!mounted) return;
    if (_requestInFlight) return;
    _requestInFlight = true;

    if (_kProvidersDebug) {
      _d('AllTimeTimelineNotifier: requesting timeline...');
    }

    _repository.getTimeline().then((_) {
      if (_kProvidersDebug) {
        _d('AllTimeTimelineNotifier: getTimeline() request sent');
      }
    }).catchError((e, _) {
      _d('AllTimeTimelineNotifier.getTimeline failed: $e');
    }).whenComplete(() {
      _requestInFlight = false;
    });
  }

  Future<void> refresh() async {
    if (!mounted) return;
    if (!_repository.isConnected) {
      try {
        await _repository.connect();
      } catch (e) {
        if (_kProvidersDebug) {
          _d('AllTimeTimelineNotifier.refresh connect failed: $e');
        }
      }
    }
    if (!mounted) return;
    _requestTimelineIfIdle();
  }

  @override
  void dispose() {
    // Cancel streams first to prevent late events updating state after dispose
    try {
      _timelineSub?.cancel();
    } catch (_) {}
    _timelineSub = null;

    try {
      _connSub?.cancel();
    } catch (_) {}
    _connSub = null;

    super.dispose();
  }
}


class SessionCountsNotifier
    extends StateNotifier<AsyncValue<Map<int, int>>> {
  final TimeTrackerRepository _repository;

  SessionCountsNotifier(this._repository) : super(const AsyncValue.loading()) {
    loadSessionCounts();
    _listenToSessionCountsUpdates();
  }

  Future<void> loadSessionCounts() async {
    try {
      if (!mounted) return;
      _setLoadingIfNoValue(state, (v) => state = v);

      if (_isWsRepo(_repository)) {
        await _repository.getSessionCounts();
      } else {
        final sessionCounts = await _repository.getSessionCounts();
        if (mounted) state = AsyncValue.data(sessionCounts);
      }
    } catch (error) {
      if (!mounted) return;
      if (state.hasValue) {
        _d('SessionCountsNotifier.loadSessionCounts failed, keeping cached data: $error');
      } else {
        state = const AsyncValue.data(<int, int>{});
      }
    }
  }

  Future<void> refresh() async {
    await loadSessionCounts();
  }

  void _listenToSessionCountsUpdates() {
    final ws = _asWs(_repository);
    if (ws == null) return;
    ws.sessionCountsStream.listen((counts) {
      if (mounted) state = AsyncValue.data(counts);
    });
  }
}

// ---------- Search and Filter State ----------

enum AppSortBy { name, duration, launches, lastUsed }
enum SortOrder { ascending, descending }

class AppSearchState {
  final String searchQuery;
  final AppSortBy sortBy;
  final SortOrder sortOrder;

  const AppSearchState({
    this.searchQuery = '',
    this.sortBy = AppSortBy.name,
    this.sortOrder = SortOrder.ascending,
  });

  AppSearchState copyWith({
    String? searchQuery,
    AppSortBy? sortBy,
    SortOrder? sortOrder,
  }) {
    return AppSearchState(
      searchQuery: searchQuery ?? this.searchQuery,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'searchQuery': searchQuery,
      'sortBy': sortBy.index,
      'sortOrder': sortOrder.index,
    };
  }

  factory AppSearchState.fromJson(Map<String, dynamic> json) {
    return AppSearchState(
      searchQuery: json['searchQuery'] ?? '',
      sortBy: AppSortBy.values[json['sortBy'] ?? 0],
      sortOrder: SortOrder.values[json['sortOrder'] ?? 0],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppSearchState &&
        other.searchQuery == searchQuery &&
        other.sortBy == sortBy &&
        other.sortOrder == sortOrder;
  }

  @override
  int get hashCode => Object.hash(searchQuery, sortBy, sortOrder);
}

class AppSearchNotifier extends StateNotifier<AppSearchState> {
  static const String _appSearchKey = 'app_search_state';
  Timer? _saveTimer;

  AppSearchNotifier() : super(const AppSearchState()) {
    _loadSearchState();
  }

  @override
  void dispose() {
    _saveTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadSearchState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final searchJson = prefs.getString(_appSearchKey);
      if (searchJson != null) {
        final Map<String, dynamic> json = jsonDecode(searchJson);
        state = AppSearchState.fromJson(json);
      }
    } catch (e) {
      _d('Failed to load app search state: $e');
    }
  }

  Future<void> _saveSearchState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(state.toJson());
      await prefs.setString(_appSearchKey, jsonString);
    } catch (e) {
      _d('Failed to save app search state: $e');
    }
  }

  void _debouncedSave() {
    _saveTimer?.cancel();
    _saveTimer = Timer(const Duration(milliseconds: 500), _saveSearchState);
  }

  void updateSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
    _debouncedSave();
  }

  void updateSortBy(AppSortBy sortBy) {
    state = state.copyWith(sortBy: sortBy);
    _debouncedSave();
  }

  void updateSortOrder(SortOrder sortOrder) {
    state = state.copyWith(sortOrder: sortOrder);
    _debouncedSave();
  }

  void toggleSortOrder() {
    final newOrder =
        state.sortOrder == SortOrder.ascending ? SortOrder.descending : SortOrder.ascending;
    state = state.copyWith(sortOrder: newOrder);
    _debouncedSave();
  }

  void clearSearch() {
    state = state.copyWith(searchQuery: '');
    _debouncedSave();
  }

  void clearAllFilters() {
    _saveTimer?.cancel();
    state = const AppSearchState();
    _saveSearchState();
  }
}

// ---------- Connection Status ----------

enum ConnectionType { websocket, http, disconnected }

class ConnectionStatus {
  final bool isConnected;
  final ConnectionType connectionType;
  final String? errorMessage;

  const ConnectionStatus({
    required this.isConnected,
    required this.connectionType,
    this.errorMessage,
  });

  ConnectionStatus copyWith({
    bool? isConnected,
    ConnectionType? connectionType,
    String? errorMessage,
  }) {
    return ConnectionStatus(
      isConnected: isConnected ?? this.isConnected,
      connectionType: connectionType ?? this.connectionType,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class ConnectionStatusNotifier extends StateNotifier<ConnectionStatus> {
  TimeTrackerRepository _repository;
  Timer? _statusCheckTimer;
  StreamSubscription<bool>? _connectionStateSubscription;

  ConnectionStatusNotifier(this._repository)
      : super(const ConnectionStatus(
          isConnected: false,
          connectionType: ConnectionType.disconnected,
        )) {
    _initializeConnection();
    _setupConnectionStateListener();
  }

  Future<void> _initializeConnection() async {
    if (!mounted) return;

    try {
      await _repository.connect();
      if (mounted) _updateConnectionStatus();
    } catch (e) {
      if (mounted) {
        state = ConnectionStatus(
          isConnected: false,
          connectionType: ConnectionType.disconnected,
          errorMessage: e.toString(),
        );
      }
    }
  }

  void _setupConnectionStateListener() {
    final ws = _asWs(_repository);
    if (ws != null) {
      // Listen to connection state immediately
      _connectionStateSubscription = ws.connectionStateStream.listen(
        (isConnected) {
          if (mounted) {
            state = ConnectionStatus(
              isConnected: isConnected,
              connectionType:
                  isConnected ? ConnectionType.websocket : ConnectionType.disconnected,
            );
          }
        },
        onError: (error) {
          if (mounted) {
            state = ConnectionStatus(
              isConnected: false,
              connectionType: ConnectionType.disconnected,
              errorMessage: error.toString(),
            );
          }
        },
      );

      // Reduced frequency periodic updates as fallback
      _statusCheckTimer =
          Timer.periodic(const Duration(seconds: 5), (_) => _updateConnectionStatus());

      _updateConnectionStatus();
    } else {
      _startStatusMonitoring();
    }
  }

  void _startStatusMonitoring() {
    _statusCheckTimer =
        Timer.periodic(const Duration(seconds: 5), (_) => _updateConnectionStatus());
  }

  void _updateConnectionStatus() {
    if (!mounted) return;

    final ws = _asWs(_repository);
    if (ws != null) {
      final isConnected = ws.isConnected;
      state = ConnectionStatus(
        isConnected: isConnected,
        connectionType: isConnected ? ConnectionType.websocket : ConnectionType.disconnected,
      );
    } else {
      state = const ConnectionStatus(
        isConnected: true,
        connectionType: ConnectionType.http,
      );
    }
  }

  @override
  void dispose() {
    _statusCheckTimer?.cancel();
    _connectionStateSubscription?.cancel();
    super.dispose();
  }

  void updateRepository(TimeTrackerRepository newRepository) {
    if (!mounted) return;

    _statusCheckTimer?.cancel();
    _connectionStateSubscription?.cancel();

    _repository = newRepository;

    // Initialize immediately without delay
    if (mounted) {
      _initializeConnection();
      _setupConnectionStateListener();
    }
  }
}
