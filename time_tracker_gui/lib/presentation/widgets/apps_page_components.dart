import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import '../providers/app_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';
import 'app_list_widget.dart';
import 'common_ui_components.dart';
import 'dialog_components.dart';

class AppsPageComponents {
  static Widget buildGamingAppsHeader(bool isDark, ThemeData theme, WidgetRef ref) {
    final searchState = ref.watch(appSearchProvider);
    final hasActiveFilters = searchState.searchQuery.isNotEmpty ||
                           searchState.sortBy != AppSortBy.name ||
                           searchState.sortOrder != SortOrder.ascending;

    return _SearchHeaderWidget(
      searchState: searchState,
      hasActiveFilters: hasActiveFilters,
      isDark: isDark,
      theme: theme,
      ref: ref,
    );
  }

  static Widget buildFilterChip(String label, VoidCallback onRemove, bool isDark, ThemeData theme) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UIConstants.spacingS,
        vertical: UIConstants.spacingXS,
      ),
      decoration: BoxDecoration(
        color: isDark 
          ? AppTheme.primaryColor.withOpacity(0.2)
          : theme.colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(UIConstants.radiusS),
        border: Border.all(
          color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: UIConstants.spacingXS),
          GestureDetector(
            onTap: onRemove,
            child: Icon(
              Icons.close,
              size: 14,
              color: isDark ? AppTheme.primaryColor : theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  static String _getSortByLabel(AppSortBy sortBy) {
    switch (sortBy) {
      case AppSortBy.name:
        return 'Name';
      case AppSortBy.duration:
        return 'Total Time';
      case AppSortBy.launches:
        return 'Times Played';
      case AppSortBy.lastUsed:
        return 'Last Played';
    }
  }

  static String _getSortOrderLabel(SortOrder order) {
    switch (order) {
      case SortOrder.ascending:
        return 'Ascending';
      case SortOrder.descending:
        return 'Descending';
    }
  }

  static Widget buildEmptyGamesLibrary(bool isDark, ThemeData theme, WidgetRef ref) {
    return Center(
      child: ModernContainer(
        padding: const EdgeInsets.all(DesignTokens.spacingXXL),
        child: ModernColumn(
          spacing: DesignTokens.spacingL,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(DesignTokens.spacingXL),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary500.withOpacity(0.2),
                    AppColors.secondary500.withOpacity(0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.games,
                size: 64,
                color: AppColors.primary500,
              ),
            ),
            Text(
              'No Games in Library',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w700,
                color: AppColors.neutral900,
              ),
            ),
            Text(
              'Start building your gaming library to track sessions\nand discover new adventures!',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.neutral600,
              ),
              textAlign: TextAlign.center,
            ),
            PrimaryButton(
              onPressed: () => DialogComponents.showAddAppDialog(ref.context, ref),
              icon: Icons.add,
              child: const Text('Add Your First Game'),
            ),
          ],
        ),
      ),
    );
  }
}

class _SearchHeaderWidget extends ConsumerStatefulWidget {
  final AppSearchState searchState;
  final bool hasActiveFilters;
  final bool isDark;
  final ThemeData theme;
  final WidgetRef ref;

  const _SearchHeaderWidget({
    required this.searchState,
    required this.hasActiveFilters,
    required this.isDark,
    required this.theme,
    required this.ref,
  });

  @override
  _SearchHeaderWidgetState createState() => _SearchHeaderWidgetState();
}

class _SearchHeaderWidgetState extends ConsumerState<_SearchHeaderWidget> {
  late final TextEditingController _searchController;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.searchState.searchQuery);
  }

  @override
  void didUpdateWidget(_SearchHeaderWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update the controller text when the search query changes from outside
    if (_searchController.text != widget.searchState.searchQuery) {
      _searchController.text = widget.searchState.searchQuery;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ModernContainer(
      padding: const EdgeInsets.all(DesignTokens.spacingL),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1200),
          child: ModernColumn(
            spacing: DesignTokens.spacingM,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ModernRow(
                spacing: DesignTokens.spacingM,
                children: [
                  Expanded(
                    child: ModernTextField(
                      hintText: 'Search your gaming library...',
                      controller: _searchController,
                      onChanged: (value) => widget.ref.read(appSearchProvider.notifier).updateSearchQuery(value),
                      prefixIcon: const Icon(Icons.search),
                      variant: InputVariant.filled,
                    ),
                  ),
                  Stack(
                    children: [
                      SecondaryButton(
                        onPressed: () => DialogComponents.showFilterDialog(context, widget.ref),
                        icon: Icons.filter_list,
                        size: ButtonSize.medium,
                        child: const Text('Filter'),
                      ),
                      if (widget.hasActiveFilters)
                        Positioned(
                          right: 8,
                          top: 8,
                          child: StatusIndicator(
                            status: StatusType.tracking,
                            size: 8,
                          ),
                        ),
                    ],
                  ),
                  if (widget.hasActiveFilters)
                    OutlinedModernButton(
                      onPressed: () => widget.ref.read(appSearchProvider.notifier).clearAllFilters(),
                      icon: Icons.clear,
                      size: ButtonSize.medium,
                      child: const Text('Clear'),
                    ),
                ],
              ),
              if (widget.hasActiveFilters) ...[
                SizedBox(height: UIConstants.spacingS),
                Wrap(
                  spacing: UIConstants.spacingS,
                  children: [
                    if (widget.searchState.searchQuery.isNotEmpty)
                      AppsPageComponents.buildFilterChip(
                        'Search: "${widget.searchState.searchQuery}"',
                        () => widget.ref.read(appSearchProvider.notifier).clearSearch(),
                        widget.isDark,
                        widget.theme,
                      ),
                    if (widget.searchState.sortBy != AppSortBy.name)
                      AppsPageComponents.buildFilterChip(
                        'Sort: ${AppsPageComponents._getSortByLabel(widget.searchState.sortBy)}',
                        () => widget.ref.read(appSearchProvider.notifier).updateSortBy(AppSortBy.name),
                        widget.isDark,
                        widget.theme,
                      ),
                    if (widget.searchState.sortOrder != SortOrder.ascending)
                      AppsPageComponents.buildFilterChip(
                        'Order: ${AppsPageComponents._getSortOrderLabel(widget.searchState.sortOrder)}',
                        () => widget.ref.read(appSearchProvider.notifier).updateSortOrder(SortOrder.ascending),
                        widget.isDark,
                        widget.theme,
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}