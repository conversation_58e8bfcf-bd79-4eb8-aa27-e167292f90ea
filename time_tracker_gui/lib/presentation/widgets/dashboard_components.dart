import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'dart:ui';
import '../providers/app_providers.dart';
import '../providers/poster_providers.dart';
import '../providers/daily_session_provider.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import '../../app/design_system/design_system.dart';
import '../../data/models/app_model.dart';
import '../../data/models/poster_model.dart';
import '../../util/time_util.dart';
import 'tracking_controls.dart';
import 'app_list_widget.dart';
import 'common_ui_components.dart';

class DashboardComponents {
  static Widget buildFrostedTrackingCard(AsyncValue trackingStatus, bool isDark, ThemeData theme) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UIConstants.radiusXL),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
        child: Container(
          padding: EdgeInsets.all(UIConstants.spacingXL.w),
          decoration: BoxDecoration(
            color: isDark
              ? Colors.black.withOpacity(0.3)
              : theme.colorScheme.surface.withOpacity(0.8),
            borderRadius: BorderRadius.circular(UIConstants.radiusXL),
            border: Border.all(
              color: isDark
                ? Colors.white.withOpacity(0.2)
                : AppTheme.neutralGray300.withOpacity(0.5),
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(UIConstants.spacingM.w),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(UIConstants.radiusM),
                      border: Border.all(
                        color: AppTheme.successColor.withOpacity(0.5),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.track_changes,
                      color: AppTheme.successColor,
                      size: UIConstants.iconM,
                    ),
                  ),
                  SizedBox(width: UIConstants.spacingM.w),
                  Text(
                    'Session Tracking',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: isDark ? Colors.white : AppTheme.neutralGray900,
                    ),
                  ),
                ],
              ),
              SizedBox(height: UIConstants.spacingL.h),
              trackingStatus.when(
                data: (status) => TrackingControls(status: status),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => CommonUIComponents.buildErrorState('Failed to load tracking status'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget buildEnhancedStatsGrid(AsyncValue trackingStatus, AsyncValue apps, bool isDark, ThemeData theme) {
    return ResponsiveLayout(
      mobile: ModernColumn(
        spacing: DesignTokens.spacingM,
        children: [
          _buildEnhancedTotalGamesCard(apps, isDark, theme),
          _buildEnhancedTodayTimeCard(isDark, theme),
        ],
      ),
      tablet: ModernRow(
        spacing: DesignTokens.spacingL,
        children: [
          Expanded(child: _buildEnhancedTotalGamesCard(apps, isDark, theme)),
          Expanded(child: _buildEnhancedTodayTimeCard(isDark, theme)),
        ],
      ),
      desktop: ModernRow(
        spacing: DesignTokens.spacingL,
        children: [
          Expanded(child: _buildEnhancedTotalGamesCard(apps, isDark, theme)),
          Expanded(child: _buildEnhancedTodayTimeCard(isDark, theme)),
        ],
      ),
    );
  }

  static Widget _buildEnhancedTotalGamesCard(AsyncValue apps, bool isDark, ThemeData theme) {
    return apps.when(
      data: (appList) => ModernStatsCard(
        title: 'Games Library',
        value: '${appList.length}',
        subtitle: 'games tracked',
        icon: Icons.library_books,
        accentColor: isDark ? AppColors.primary500 : theme.colorScheme.primary,
      ),
      loading: () => ModernStatsCard(
        title: 'Games Library',
        value: '...',
        subtitle: 'loading',
        icon: Icons.library_books,
        accentColor: AppColors.neutral500,
      ),
      error: (_, __) => ModernStatsCard(
        title: 'Games Library',
        value: 'Error',
        subtitle: 'failed to load',
        icon: Icons.error_outline,
        accentColor: AppColors.error,
      ),
    );
  }

  static Widget _buildEnhancedTodayTimeCard(bool isDark, ThemeData theme) {
    return Consumer(
      builder: (context, ref, child) {
        final todaySession = ref.watch(todaySessionProvider);

        return todaySession.when(
          data: (data) => ModernStatsCard(
            title: 'Today\'s Playtime',
            value: TimeUtil.formatDurationFromMinutes(data.totalMinutes),
            subtitle: 'gaming session',
            icon: Icons.schedule,
            accentColor: AppColors.success,
          ),
          loading: () => ModernStatsCard(
            title: 'Today\'s Playtime',
            value: '...',
            subtitle: 'loading',
            icon: Icons.schedule,
            accentColor: AppColors.neutral500,
          ),
          error: (_, __) => ModernStatsCard(
            title: 'Today\'s Playtime',
            value: '0m',
            subtitle: 'no data',
            icon: Icons.schedule,
            accentColor: AppColors.success,
          ),
        );
      },
    );
  }

  static Widget _buildEnhancedStatDisplay(String value, String subtitle, IconData icon, Color color, bool isDark, ThemeData theme) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(UIConstants.spacingL.w),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(UIConstants.radiusL),
            border: Border.all(
              color: color.withOpacity(0.5),
              width: 2,
            ),
          ),
          child: Icon(
            icon,
            color: color,
            size: UIConstants.iconL,
          ),
        ),
        SizedBox(width: UIConstants.spacingL.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: color,
                ),
              ),
              Text(
                subtitle,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDark ? Colors.white.withOpacity(0.6) : AppTheme.neutralGray600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  static Widget buildGamingAppsSection(AsyncValue<List<AppModel>> apps, bool isDark, ThemeData theme, VoidCallback onViewAll) {
    return ModernInfoCard(
      title: 'Recent Games',
      icon: Icons.games,
      actions: [
        PrimaryButton(
          onPressed: onViewAll,
          size: ButtonSize.small,
          child: const Text('View All'),
        ),
      ],
      child: apps.when(
        data: (appList) => appList.isEmpty
            ? _buildEmptyGamesState()
            : AppListWidget(
                apps: appList.take(5).toList(),
                isCompact: true,
              ),
        loading: () => const ModernContainer(
          padding: EdgeInsets.all(DesignTokens.spacingXL),
          child: Center(child: LoadingSpinner()),
        ),
        error: (error, _) => _buildErrorGamesState(),
      ),
    );
  }

  static Widget _buildEmptyGamesState() {
    return ModernContainer(
      padding: const EdgeInsets.all(DesignTokens.spacingXL),
      child: ModernColumn(
        spacing: DesignTokens.spacingM,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.games_outlined,
            size: 48,
            color: AppColors.neutral500,
          ),
          Text(
            'No games tracked yet',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.neutral700,
            ),
          ),
          Text(
            'Start playing to see your library here',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.neutral500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  static Widget _buildErrorGamesState() {
    return ModernContainer(
      padding: const EdgeInsets.all(DesignTokens.spacingXL),
      child: ModernColumn(
        spacing: DesignTokens.spacingM,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: AppColors.error,
          ),
          Text(
            'Failed to load games',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.error,
            ),
          ),
        ],
      ),
    );
  }

  static Widget buildFeaturedPostersSection(AsyncValue postersAsyncValue, bool isDark, ThemeData theme) {
    return ModernInfoCard(
      title: 'Featured Content',
      icon: Icons.featured_play_list,
      child: postersAsyncValue.when(
        data: (posters) {
          if (posters.isEmpty) {
            return _buildEmptyFeaturedState();
          } else {
            return SizedBox(
              height: 250,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: posters.length,
                itemBuilder: (context, index) {
                  final poster = posters[index];
                  return Padding(
                    padding: const EdgeInsets.only(right: DesignTokens.spacingL),
                    child: _buildGamingPosterItem(poster, isDark, theme),
                  );
                },
              ),
            );
          }
        },
        loading: () => const ModernContainer(
          padding: EdgeInsets.all(DesignTokens.spacingXL),
          child: Center(child: LoadingSpinner()),
        ),
        error: (error, _) => _buildErrorFeaturedState(),
      ),
    );
  }

  static Widget _buildEmptyFeaturedState() {
    return ModernContainer(
      padding: const EdgeInsets.all(DesignTokens.spacingXL),
      child: ModernColumn(
        spacing: DesignTokens.spacingM,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.featured_play_list_outlined,
            size: 48,
            color: AppColors.neutral500,
          ),
          Text(
            'No featured content available',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.neutral700,
            ),
          ),
          Text(
            'Check back later for gaming highlights',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.neutral500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  static Widget _buildErrorFeaturedState() {
    return ModernContainer(
      padding: const EdgeInsets.all(DesignTokens.spacingXL),
      child: ModernColumn(
        spacing: DesignTokens.spacingM,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: AppColors.error,
          ),
          Text(
            'Failed to load featured content',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppColors.error,
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildGamingPosterItem(Poster poster, bool isDark, ThemeData theme) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(UIConstants.radiusL),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Container(
          width: 200.w,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark ? [
                theme.cardBg.withOpacity(0.8),
                theme.cardBg.withOpacity(0.6),
              ] : [
                theme.colorScheme.surface.withOpacity(0.8),
                theme.colorScheme.surfaceContainerHighest.withOpacity(0.6),
              ],
            ),
            borderRadius: BorderRadius.circular(UIConstants.radiusL),
            border: Border.all(
              color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray300,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(UIConstants.radiusL),
                    topRight: Radius.circular(UIConstants.radiusL),
                  ),
                  child: Image.network(
                    poster.backgroundImage,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: isDark ? [
                            AppTheme.neutralGray700,
                            AppTheme.neutralGray800,
                          ] : [
                            AppTheme.neutralGray300,
                            AppTheme.neutralGray400,
                          ],
                        ),
                      ),
                      child: Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: isDark ? AppTheme.neutralGray500 : AppTheme.neutralGray600,
                          size: UIConstants.iconL,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(UIConstants.spacingM.w),
                child: Text(
                  poster.name,
                  textAlign: TextAlign.center,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isDark ? AppTheme.neutralGray100 : AppTheme.neutralGray900,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}