import 'package:flutter/material.dart';

import '../../../data/models/app_model.dart';
import '../../../util/time_util.dart';
import '../../../app/design_system/design_system.dart';
class LongestSessionCard extends StatelessWidget {
  final AppModel app;

  const LongestSessionCard({
    super.key,
    required this.app,
  });

  @override
  Widget build(BuildContext context) {
    final longestSession = app.longestSession ?? 0;
    final formattedDuration = TimeUtil.formatDuration(longestSession);
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600; // Mobile breakpoint

    return ModernInfoCard(
      title: 'Longest Session',
      icon: Icons.timer,
      child: ModernColumn(
        spacing: isMobile ? DesignTokens.spacingXS : DesignTokens.spacingS,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            formattedDuration,
            style: TextStyle(
              fontSize: isMobile ? 24 : 32, // Smaller font on mobile
              fontWeight: FontWeight.bold,
              color: AppColors.steamBlue,
            ),
          ),
          Text(
            'Your longest gaming session for this app',
            style: TextStyle(
              fontSize: isMobile ? 12 : 14, // Smaller font on mobile
              color: AppColors.neutral600,
            ),
          ),
        ],
      ),
    );
  }


}