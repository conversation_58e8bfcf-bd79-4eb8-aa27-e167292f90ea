import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/app_model.dart';
import '../../providers/app_providers.dart';
import '../../../app/design_system/design_system.dart';

class AppStatsRow extends ConsumerWidget {
  final AppModel app;

  const AppStatsRow({
    super.key,
    required this.app,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    // Use Column layout on very small screens to prevent overflow
    if (isMobile && screenWidth < 500) {
      return ModernColumn(
        spacing: DesignTokens.spacingS,
        children: [
          ModernStatsCard(
            title: 'Launches',
            value: '${app.launches ?? 0}',
            icon: Icons.launch,
            accentColor: AppColors.steamBlue,
          ),
          ModernStatsCard(
            title: 'Avg Session',
            value: _formatDuration(_calculateAverageSession()),
            icon: Icons.timer,
            accentColor: AppColors.success,
          ),
          ModernStatsCard(
            title: 'Last Played',
            value: _formatLastPlayed(ref),
            icon: Icons.schedule,
            accentColor: AppColors.warning,
          ),
        ],
      );
    }

    // Use intrinsic width for mobile to prevent overflow
    if (isMobile) {
      return IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: ModernStatsCard(
                title: 'Launches',
                value: '${app.launches ?? 0}',
                icon: Icons.launch,
                accentColor: AppColors.steamBlue,
              ),
            ),
            SizedBox(width: DesignTokens.spacingS),
            Expanded(
              child: ModernStatsCard(
                title: 'Avg Session',
                value: _formatDuration(_calculateAverageSession()),
                icon: Icons.timer,
                accentColor: AppColors.success,
              ),
            ),
            SizedBox(width: DesignTokens.spacingS),
            Expanded(
              child: ModernStatsCard(
                title: 'Last Played',
                value: _formatLastPlayed(ref),
                icon: Icons.schedule,
                accentColor: AppColors.warning,
              ),
            ),
          ],
        ),
      );
    }

    return ModernRow(
      spacing: DesignTokens.spacingM,
      children: [
        Expanded(
          child: ModernStatsCard(
            title: 'Launches',
            value: '${app.launches ?? 0}',
            icon: Icons.launch,
            accentColor: AppColors.steamBlue,
          ),
        ),
        Expanded(
          child: ModernStatsCard(
            title: 'Avg Session',
            value: _formatDuration(_calculateAverageSession()),
            icon: Icons.timer,
            accentColor: AppColors.success,
          ),
        ),
        Expanded(
          child: ModernStatsCard(
            title: 'Last Played',
            value: _formatLastPlayed(ref),
            icon: Icons.schedule,
            accentColor: AppColors.warning,
          ),
        ),
      ],
    );
  }



  int _calculateAverageSession() {
    final totalDuration = app.duration ?? 0;
    final launches = app.launches ?? 1;
    return launches > 0 ? totalDuration ~/ launches : 0;
  }

  String _formatLastPlayed(WidgetRef ref) {
    // Use the cached apps with last used provider for better performance
    final appsWithLastUsed = ref.watch(appsWithLastUsedCacheProvider);

    return appsWithLastUsed.when(
      data: (appWithLastUsedList) {
        // Find this app in the list
        final appWithLastUsed = appWithLastUsedList
            .where((item) => item.app.id == app.id)
            .firstOrNull;

        if (appWithLastUsed?.lastUsedDate == null) return 'Never';

        final lastDate = appWithLastUsed!.lastUsedDate!;
        final now = DateTime.now();
        final difference = now.difference(lastDate).inDays;

        return switch (difference) {
          0 => 'Today',
          1 => 'Yesterday',
          < 7 => '${difference}d ago',
          < 30 => '${difference ~/ 7}w ago',
          < 365 => '${difference ~/ 30}mo ago',
          _ => _formatActualDate(lastDate),
        };
      },
      loading: () => '...',
      error: (_, __) => 'Never',
    );
  }

  String _formatActualDate(DateTime date) {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];

    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  String _formatDuration(int minutes) {
    return minutes < 60
      ? '${minutes}m'
      : '${minutes ~/ 60}h ${minutes % 60}m';
  }
}