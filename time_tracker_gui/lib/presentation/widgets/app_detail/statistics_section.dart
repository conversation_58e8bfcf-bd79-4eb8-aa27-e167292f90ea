import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/app_model.dart';
import '../../providers/app_providers.dart';
import '../../../app/design_system/design_system.dart';
import '../../../util/time_util.dart';
import 'app_detail_enums.dart';
import 'session_service.dart';

class StatisticsSection extends ConsumerWidget {
  final String appId;
  final TimeRange selectedTimeRange;
  final CheckpointModel? selectedCheckpoint;

  const StatisticsSection({
    super.key,
    required this.appId,
    required this.selectedTimeRange,
    this.selectedCheckpoint,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final timeline = ref.watch(timelineProvider);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return ModernContainer(
      padding: const EdgeInsets.all(DesignTokens.spacingL),
      backgroundColor: isDark
        ? theme.colorScheme.surfaceContainerHighest
        : theme.colorScheme.surface,
      borderRadius: BorderRadius.circular(DesignTokens.radiusL),
      borderColor: selectedCheckpoint != null
        ? Color(int.parse(selectedCheckpoint!.color.replaceFirst('#', '0xFF'))).withValues(alpha: 0.3)
        : isDark ? AppColors.neutral700 : AppColors.neutral300,
      borderWidth: selectedCheckpoint != null ? 2 : 1,
      child: ModernColumn(
        spacing: DesignTokens.spacingL,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(theme, isDark),
          _buildStatisticsContent(timeline, theme, isDark),
        ],
      ),
    );
  }

  Widget _buildHeader(ThemeData theme, bool isDark) {
    final filterDesc = SessionService.getFilterDescription(selectedTimeRange, selectedCheckpoint);

    return ModernRow(
      spacing: DesignTokens.spacingS,
      children: [
        if (selectedCheckpoint != null) ...[
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: Color(int.parse(selectedCheckpoint!.color.replaceFirst('#', '0xFF'))),
              shape: BoxShape.circle,
            ),
          ),
        ],
        Expanded(
          child: Text(
            'Usage Statistics - $filterDesc',
            style: theme.textTheme.titleLarge?.copyWith(
              color: isDark ? AppColors.neutral100 : AppColors.neutral900,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatisticsContent(AsyncValue<List<TimelineModel>> timeline, ThemeData theme, bool isDark) {
    return timeline.when(
      data: (timelineData) => _buildStatisticsData(timelineData, theme, isDark),
      loading: () => const SizedBox(
        height: 200,
        child: Center(child: CircularProgressIndicator()),
      ),
      error: (error, _) => _buildErrorState('Failed to load statistics', isDark, theme),
    );
  }

  Widget _buildStatisticsData(List<TimelineModel> timelineData, ThemeData theme, bool isDark) {
    final appSessions = timelineData.where((session) => session.appId.toString() == appId).toList();
    final stats = SessionService.calculateUsageStats(
      appSessions,
      selectedTimeRange,
      checkpoint: selectedCheckpoint,
    );

    return stats.totalSessions == 0
      ? _buildEmptyStatsState(isDark, theme)
      : _buildStatsGrid(stats, isDark, theme);
  }

  Widget _buildEmptyStatsState(bool isDark, ThemeData theme) {
    final filterDesc = SessionService.getFilterDescription(selectedTimeRange, selectedCheckpoint);

    return SizedBox(
      height: 200,
      child: ModernContainer(
        backgroundColor: isDark
          ? theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5)
          : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        borderColor: isDark
          ? AppColors.neutral700.withValues(alpha: 0.5)
          : AppColors.neutral400.withValues(alpha: 0.3),
        child: Center(
          child: ModernColumn(
            spacing: DesignTokens.spacingM,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.analytics_outlined,
                size: DesignTokens.iconXL,
                color: isDark ? AppColors.neutral600 : AppColors.neutral500,
              ),
              Text(
                'No data for $filterDesc',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: isDark ? AppColors.neutral400 : AppColors.neutral600,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                selectedCheckpoint != null
                  ? 'Play some games with this checkpoint to see statistics'
                  : 'Play some games to see statistics here',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isDark ? AppColors.neutral500 : AppColors.neutral600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods for ultra-small device detection
  bool _isUltraSmall(BuildContext context) => MediaQuery.of(context).size.width < 360;
  bool _isSmallMobile(BuildContext context) => MediaQuery.of(context).size.width < 480;

  Widget _buildStatsGrid(UsageStatistics stats, bool isDark, ThemeData theme) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isMobile = context.isMobile;
        final isTablet = context.isTablet;
        final isUltraSmall = _isUltraSmall(context);
        final isSmallMobile = _isSmallMobile(context);

        final checkpointColor = selectedCheckpoint != null
          ? Color(int.parse(selectedCheckpoint!.color.replaceFirst('#', '0xFF')))
          : null;

        // On ultra-small devices, use even more compact layout
        if (isUltraSmall) {
          return _buildUltraCompactStats(stats, checkpointColor);
        }

        // On mobile, stack all cards vertically
        if (isMobile) {
          return ModernColumn(
            spacing: isSmallMobile ? DesignTokens.spacingXS : DesignTokens.spacingS,
            children: [
              ModernStatsCard(
                title: 'Total Sessions',
                value: '${stats.totalSessions}',
                icon: Icons.play_circle_outline,
                accentColor: checkpointColor ?? AppColors.steamBlue,
              ),
              ModernStatsCard(
                title: 'Total Time',
                value: TimeUtil.formatMinutes(stats.totalDuration),
                icon: Icons.schedule,
                accentColor: checkpointColor?.withValues(alpha: 0.9) ?? AppColors.success,
              ),
              ModernStatsCard(
                title: 'Average Session',
                value: TimeUtil.formatMinutes(stats.averageSession),
                icon: Icons.timer_outlined,
                accentColor: checkpointColor?.withValues(alpha: 0.8) ?? AppColors.warning,
              ),
              ModernStatsCard(
                title: 'Daily Average',
                value: TimeUtil.formatMinutes(stats.dailyAverage),
                icon: Icons.today_outlined,
                accentColor: checkpointColor?.withValues(alpha: 0.7) ?? AppColors.info,
              ),
              ModernInfoCard(
                title: 'Longest Session',
                icon: Icons.emoji_events,
                child: Text(
                  TimeUtil.formatMinutes(stats.longestSession),
                  style: TextStyle(
                    fontSize: isSmallMobile ? 20 : 24,
                    fontWeight: FontWeight.bold,
                    color: checkpointColor ?? AppColors.steamBlue,
                  ),
                ),
              ),
            ],
          );
        }

        // On tablet and desktop, use responsive grid layout
        return ModernColumn(
          spacing: DesignTokens.spacingM,
          children: [
            ResponsiveColumns(
              spacing: DesignTokens.spacingM,
              breakpoint: DesignTokens.breakpointTablet,
              children: [
                ModernStatsCard(
                  title: 'Total Sessions',
                  value: '${stats.totalSessions}',
                  icon: Icons.play_circle_outline,
                  accentColor: checkpointColor ?? AppColors.steamBlue,
                ),
                ModernStatsCard(
                  title: 'Total Time',
                  value: TimeUtil.formatMinutes(stats.totalDuration),
                  icon: Icons.schedule,
                  accentColor: checkpointColor?.withValues(alpha: 0.9) ?? AppColors.success,
                ),
              ],
            ),
            ResponsiveColumns(
              spacing: DesignTokens.spacingM,
              breakpoint: DesignTokens.breakpointTablet,
              children: [
                ModernStatsCard(
                  title: 'Average Session',
                  value: TimeUtil.formatMinutes(stats.averageSession),
                  icon: Icons.timer_outlined,
                  accentColor: checkpointColor?.withValues(alpha: 0.8) ?? AppColors.warning,
                ),
                ModernStatsCard(
                  title: 'Daily Average',
                  value: TimeUtil.formatMinutes(stats.dailyAverage),
                  icon: Icons.today_outlined,
                  accentColor: checkpointColor?.withValues(alpha: 0.7) ?? AppColors.info,
                ),
              ],
            ),
            ModernInfoCard(
              title: 'Longest Session',
              icon: Icons.emoji_events,
              child: Text(
                TimeUtil.formatMinutes(stats.longestSession),
                style: TextStyle(
                  fontSize: isTablet ? 28 : 32,
                  fontWeight: FontWeight.bold,
                  color: checkpointColor ?? AppColors.steamBlue,
                ),
              ),
            ),
          ],
        );
      },
    );
  }



  Widget _buildErrorState(String message, bool isDark, ThemeData theme) {
    return SizedBox(
      height: 200,
      child: ModernContainer(
        backgroundColor: isDark
          ? theme.colorScheme.surfaceContainerHighest
          : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(DesignTokens.radiusM),
        borderColor: theme.colorScheme.error.withValues(alpha: 0.3),
        child: Center(
          child: ModernColumn(
            spacing: DesignTokens.spacingM,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: DesignTokens.iconXL,
                color: theme.colorScheme.error,
              ),
              Text(
                message,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.error,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUltraCompactStats(UsageStatistics stats, Color? checkpointColor) {
    return ModernColumn(
      spacing: DesignTokens.spacingXS,
      children: [
        // Two most important stats in a row
        Row(
          children: [
            Expanded(
              child: _buildCompactStatItem(
                'Sessions',
                '${stats.totalSessions}',
                Icons.play_circle_outline,
                checkpointColor ?? AppColors.steamBlue,
              ),
            ),
            const SizedBox(width: DesignTokens.spacingXS),
            Expanded(
              child: _buildCompactStatItem(
                'Total Time',
                TimeUtil.formatMinutes(stats.totalDuration),
                Icons.schedule,
                checkpointColor?.withValues(alpha: 0.9) ?? AppColors.success,
              ),
            ),
          ],
        ),
        // Two secondary stats in a row
        Row(
          children: [
            Expanded(
              child: _buildCompactStatItem(
                'Avg Session',
                TimeUtil.formatMinutes(stats.averageSession),
                Icons.timer_outlined,
                checkpointColor?.withValues(alpha: 0.8) ?? AppColors.warning,
              ),
            ),
            const SizedBox(width: DesignTokens.spacingXS),
            Expanded(
              child: _buildCompactStatItem(
                'Daily Avg',
                TimeUtil.formatMinutes(stats.dailyAverage),
                Icons.today_outlined,
                checkpointColor?.withValues(alpha: 0.7) ?? AppColors.info,
              ),
            ),
          ],
        ),
        // Longest session as a single highlight
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(DesignTokens.spacingS),
          decoration: BoxDecoration(
            color: (checkpointColor ?? AppColors.steamBlue).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(DesignTokens.radiusS),
            border: Border.all(
              color: (checkpointColor ?? AppColors.steamBlue).withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.emoji_events,
                color: checkpointColor ?? AppColors.steamBlue,
                size: DesignTokens.iconS,
              ),
              const SizedBox(width: DesignTokens.spacingS),
              Expanded(
                child: Text(
                  'Longest: ${TimeUtil.formatMinutes(stats.longestSession)}',
                  style: TextStyle(
                    fontSize: DesignTokens.fontSize12,
                    fontWeight: FontWeight.w600,
                    color: checkpointColor ?? AppColors.steamBlue,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCompactStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(DesignTokens.spacingS),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(DesignTokens.radiusS),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: DesignTokens.iconS,
          ),
          const SizedBox(height: DesignTokens.spacingXS),
          Text(
            value,
            style: TextStyle(
              fontSize: DesignTokens.fontSize14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: DesignTokens.fontSize10,
              color: color.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}

